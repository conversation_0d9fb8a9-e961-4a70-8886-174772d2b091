import 'package:flutter/material.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

class PhoneAuthScreen extends StatefulWidget {
  const PhoneAuthScreen({Key? key}) : super(key: key);

  @override
  State<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
}

class _PhoneAuthScreenState extends State<PhoneAuthScreen> {
  late TextEditingController _phoneController;
  TextEditingController? _otpController;
  bool _codeSent = false;
  bool _loading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _phoneController = TextEditingController();
  }

  @override
  void dispose() {
    // Dispose controllers before calling super.dispose()
    _phoneController.dispose();

    // Dispose OTP controller if it exists
    final localOtpController = _otpController;
    if (localOtpController != null) {
      // Clear the reference first to prevent multiple disposal attempts
      _otpController = null;
      // Then dispose the controller properly
      localOtpController.dispose();
    }

    // Always call super.dispose() last
    super.dispose();
  }

  // Request verification code
  Future<void> _requestCode() async {
    // Validate phone number
    String phoneNumber = _phoneController.text.trim();
    if (phoneNumber.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a phone number';
      });
      return;
    }

    // Format phone number if needed
    if (!phoneNumber.startsWith('+')) {
      // Add country code if not present (assuming Libya +218)
      if (phoneNumber.startsWith('0')) {
        phoneNumber = '+218${phoneNumber.substring(1)}';
      } else {
        phoneNumber = '+218$phoneNumber';
      }
    }

    setState(() {
      _loading = true;
      _errorMessage = '';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.requestOTP(phoneNumber);

      // Check for errors
      if (!success) {
        setState(() {
          _errorMessage = authProvider.error ?? 'Failed to send verification code';
          _loading = false;
        });
        return;
      }

      // Safely handle the OTP controller
      final oldController = _otpController;

      // Create a new OTP controller when code is sent
      final newController = TextEditingController();

      // Update state with the new controller
      setState(() {
        _otpController = newController;
        _codeSent = true;
        _loading = false;
      });

      // Dispose the old controller after state update if it exists
      if (oldController != null) {
        // Use addPostFrameCallback to ensure disposal happens after the build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          oldController.dispose();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _loading = false;
      });
    }
  }

  // Verify code and sign in
  Future<void> _verifyCode() async {
    // Ensure OTP controller exists
    if (_otpController == null) {
      setState(() {
        _errorMessage = 'Verification code not initialized';
      });
      return;
    }

    if (_otpController!.text.length < 4) {
      setState(() {
        _errorMessage = 'Please enter a valid verification code';
      });
      return;
    }

    setState(() {
      _loading = true;
      _errorMessage = '';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Format phone number if needed
      String phoneNumber = _phoneController.text.trim();
      if (!phoneNumber.startsWith('+')) {
        // Add country code if not present (assuming Libya +218)
        if (phoneNumber.startsWith('0')) {
          phoneNumber = '+218${phoneNumber.substring(1)}';
        } else {
          phoneNumber = '+218$phoneNumber';
        }
      }

      final success = await authProvider.verifyOTP(
        phoneNumber,
        _otpController!.text,
      );

      if (success) {
        // Navigate to the home screen
        if (!mounted) return;
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        setState(() {
          _errorMessage = authProvider.error ?? 'Verification failed';
          _loading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              // App Logo or Image
              CircleAvatar(
                radius: 60,
                backgroundColor: Theme.of(context).colorScheme.primary.withAlpha(25),
                child: Icon(
                  Icons.food_bank_outlined,
                  size: 80,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 20),
              // App Name
              Text(
                'طلباتي',
                style: TextStyle(
                  fontSize: 36,
                  fontFamily: 'Alx',
                  fontWeight: FontWeight.bold,
                  foreground: Paint()
                    ..shader = LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.secondary,
                      ],
                    ).createShader(
                      const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0),
                    ),
                ),
              ),
              const SizedBox(height: 10),
              // Subtitle
              Text(
                _codeSent
                    ? 'أدخل رمز التحقق الذي تم إرساله إلى هاتفك'
                    : 'قم بتسجيل الدخول بواسطة رقم هاتفك',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Cairo',
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 40),

              // Error message
              if (_errorMessage.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(10),
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: Colors.red),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          _errorMessage,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),

              // Phone Input or OTP Input based on state
              if (!_codeSent)
                // Phone Input
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(25),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: TextField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(15),
                      hintText: 'أدخل رقم الهاتف',
                      hintTextDirection: TextDirection.rtl,
                      prefixIcon: Icon(Icons.phone),
                    ),
                    textAlign: TextAlign.end,
                    textDirection: TextDirection.ltr,
                  ),
                )
              else
                // OTP Input
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: _otpController != null
                      ? PinCodeTextField(
                          appContext: context,
                          length: 4,
                          controller: _otpController!,
                          animationType: AnimationType.fade,
                          pinTheme: PinTheme(
                            shape: PinCodeFieldShape.box,
                            borderRadius: BorderRadius.circular(10),
                            fieldHeight: 50,
                            fieldWidth: 50,
                            activeFillColor: Colors.white,
                            activeColor: Theme.of(context).colorScheme.primary,
                            inactiveColor: Colors.grey,
                            selectedColor: Theme.of(context).colorScheme.secondary,
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            // No need to setState here
                          },
                        )
                      : const Center(
                          child: Text("Loading verification code input..."),
                        ),
                ),

              const SizedBox(height: 30),

              // Action Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _loading
                      ? null
                      : (_codeSent ? _verifyCode : _requestCode),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: _loading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          _codeSent ? 'تحقق' : 'إرسال رمز التحقق',
                          style: const TextStyle(
                            fontSize: 18,
                            fontFamily: 'Alx',
                          ),
                        ),
                ),
              ),

              // Back Button (if code sent)
              if (_codeSent)
                TextButton(
                  onPressed: () {
                    // Safely capture the OTP controller reference
                    final otpControllerToDispose = _otpController;

                    // Update state first - this prevents accessing disposed controllers
                    setState(() {
                      // Reset the code sent flag
                      _codeSent = false;

                      // Clear the OTP controller reference before disposing it
                      _otpController = null;

                      // Clear any error messages
                      _errorMessage = '';
                    });

                    // After the state is updated, dispose the OTP controller if it exists
                    if (otpControllerToDispose != null) {
                      // Use addPostFrameCallback to ensure disposal happens after the build
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (otpControllerToDispose != null) {
                          otpControllerToDispose.dispose();
                        }
                      });
                    }

                    // No need to dispose and recreate the phone controller
                    // Just keep using the existing one with the current text
                  },
                  child: const Text('العودة لتغيير رقم الهاتف'),
                ),

              // Note about SMS verification
              const Spacer(),
              const Text(
                '* سيتم إرسال رمز التحقق عبر رسالة SMS إلى رقم الهاتف المدخل',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                  fontFamily: 'Cairo',
                ),
              ),
              const Text(
                '** ملاحظة: وظيفة التحقق عبر SMS غير مفعلة حالياً',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }
}