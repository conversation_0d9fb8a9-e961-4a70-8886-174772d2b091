const sequelize = require("../config/database");
const Sequelize = require('sequelize');
const { Customer } = require('../models/init-models')(sequelize);
const { successResponse, errorResponse } = require('../utils/responseFormatter');
const Redis = require("ioredis");
const redis = new Redis();
const smsService = require('../utils/smsService');

/**
 * Request phone verification
 */
const requestPhoneVerification = async (req, res) => {
    try {
        console.log('Customer verification request:', req.body);
        const { phoneNumber } = req.body;

        if (!phoneNumber) {
            console.error('Missing phone number');
            const { response, statusCode } = errorResponse(
                'رقم الهاتف مطلوب',
                400,
                'MISSING_PHONE_NUMBER'
            );
            return res.status(statusCode).json(response);
        }

        // Find customer by phone number
        console.log('Checking phone number:', phoneNumber.trim());
        const customer = await Customer.findOne({
            where: {
                PhoneNum: phoneNumber.trim()
            }
        });

        // If customer doesn't exist, create a new one
        if (!customer) {
            console.log('Creating new customer for phone:', phoneNumber);
            await Customer.create({
                PhoneNum: phoneNumber.trim(),
                JoinDate: Sequelize.fn('GETDATE'),
                Status: 1,
                IsBanned: false
            });
        } else {
            // Check if the customer is banned
            if (customer.IsBanned) {
                console.error('Banned customer attempted login:', phoneNumber);
                const { response, statusCode } = errorResponse(
                    'تم حظر هذا الرقم من استخدام الخدمة',
                    403,
                    'CUSTOMER_BANNED'
                );
                return res.status(statusCode).json(response);
            }
        }

        // Send verification code via SMS using the SMS service
        console.log('Sending verification code via SMS to:', phoneNumber);
        const smsResult = await smsService.sendVerificationCode(phoneNumber.trim());

        if (!smsResult.success) {
            console.error('Failed to send SMS:', smsResult.message);
            const { response, statusCode } = errorResponse(
                smsResult.message || 'فشل في إرسال رمز التحقق',
                500,
                'SMS_SEND_FAILED'
            );
            return res.status(statusCode).json(response);
        }

        console.log('SMS sent successfully to:', phoneNumber);

        // Return success response
        res.status(200).json(successResponse({
            phoneNumber: smsResult.phoneNumber,
            message: smsResult.message,
            // Include verification code only in development mode for testing
            ...(process.env.NODE_ENV === 'development' && smsResult.verificationCode
                ? { verificationCode: smsResult.verificationCode }
                : {})
        }));
    } catch (error) {
        console.error('Phone verification request error:', error);
        const { response, statusCode } = errorResponse(
            'خطأ في الخادم أثناء طلب التحقق',
            500,
            'SERVER_ERROR'
        );
        res.status(statusCode).json(response);
    }
};

/**
 * Verify phone and login
 */
const verifyPhoneAndLogin = async (req, res) => {
    try {
        console.log('Customer verification attempt:', req.body);
        const { phoneNumber, verificationCode } = req.body;

        if (!phoneNumber || !verificationCode) {
            console.error('Missing credentials');
            const { response, statusCode } = errorResponse(
                'رقم الهاتف ورمز التحقق مطلوبان',
                400,
                'MISSING_CREDENTIALS'
            );
            return res.status(statusCode).json(response);
        }

        // Format phone number to match the SMS service format
        const formattedPhoneNumber = smsService.formatPhoneNumber ?
            smsService.formatPhoneNumber(phoneNumber.trim()) :
            phoneNumber.trim();

        // Check if verification code exists and is valid
        let storedVerificationData;
        try {
            storedVerificationData = await redis.get(`verification:${formattedPhoneNumber}`);
        } catch (redisError) {
            console.error('Redis error while retrieving verification code:', redisError);
            const { response, statusCode } = errorResponse(
                'خطأ في الخادم أثناء التحقق من الرمز',
                500,
                'SERVER_ERROR'
            );
            return res.status(statusCode).json(response);
        }

        if (!storedVerificationData) {
            console.error('رمز التحقق غير صالح أو منتهي الصلاحية');
            const { response, statusCode } = errorResponse(
                'رمز التحقق غير صالح أو منتهي الصلاحية',
                401,
                'EXPIRED_OR_INVALID_VERIFICATION_CODE'
            );
            return res.status(statusCode).json(response);
        }

        // Parse the stored verification data
        let storedVerification;
        try {
            storedVerification = JSON.parse(storedVerificationData);
        } catch (parseError) {
            console.error('Error parsing verification data:', parseError);
            const { response, statusCode } = errorResponse(
                'خطأ في الخادم أثناء التحقق من الرمز',
                500,
                'SERVER_ERROR'
            );
            return res.status(statusCode).json(response);
        }

        // Verify the code
        if (storedVerification.code !== verificationCode) {
            console.error('Incorrect verification code');
            const { response, statusCode } = errorResponse(
                'رمز التحقق غير صحيح',
                401,
                'INCORRECT_VERIFICATION_CODE'
            );
            return res.status(statusCode).json(response);
        }

        // Remove the verification code from Redis
        try {
            await redis.del(`verification:${formattedPhoneNumber}`);
            console.log(`Verification code removed from Redis for ${formattedPhoneNumber}`);
        } catch (redisError) {
            console.error('Redis error while deleting verification code:', redisError);
            // Continue execution even if Redis delete fails
        }

        // Find or create the customer using the original phone number format
        const [customer] = await Customer.findOrCreate({
            where: { PhoneNum: phoneNumber.trim() },
            defaults: {
                JoinDate: new Date(),
                Status: 1,
                IsBanned: false
            }
        });

        // Check if the customer is banned
        if (customer.IsBanned) {
            console.error('Banned customer attempted login:', phoneNumber);
            const { response, statusCode } = errorResponse(
                'تم حظر هذا الرقم من استخدام الخدمة',
                403,
                'CUSTOMER_BANNED'
            );
            return res.status(statusCode).json(response);
        }

        console.log('Generating tokens for customer:', customer.CustomerID);

        // Generate customer-specific tokens
        const tokens = generateCustomerTokens(customer.CustomerID);

        console.log('Setting refresh token cookie');
        res.cookie('customerRefreshToken', tokens.refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
        });

        // Set CORS headers explicitly to ensure they are present
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        console.log('Sending success response with tokens and user data');

        // Format customer data
        const customerData = {
            id: customer.CustomerID,
            phoneNumber: customer.PhoneNum,
            joinDate: customer.JoinDate,
            status: customer.Status,
            location: {
                latitude: customer.LocationLatitude,
                longitude: customer.LocationLongitude
            }
        };

        // Return the response
        res.status(200).json({
            success: true,
            tokens: {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken
            },
            user: customerData
        });
    } catch (error) {
        console.error('Phone verification error:', error);
        const { response, statusCode } = errorResponse(
            'خطأ في الخادم أثناء التحقق',
            500,
            'SERVER_ERROR'
        );
        res.status(statusCode).json(response);
    }
};

/**
 * Refresh customer token
 */
const refreshCustomerToken = async (req, res) => {
    try {
        const refreshToken = req.cookies.customerRefreshToken || req.body.refreshToken;

        if (!refreshToken) {
            const { response, statusCode } = errorResponse(
                'الرجاء تسجيل الدخول مرة أخرى',
                401,
                'NO_REFRESH_TOKEN'
            );
            return res.status(statusCode).json(response);
        }

        const jwt = require('jsonwebtoken');
        let decoded;

        try {
            decoded = jwt.verify(refreshToken, process.env.JWT_CUSTOMER_REFRESH_SECRET);
        } catch (error) {
            if (req.cookies.customerRefreshToken) {
                res.clearCookie('customerRefreshToken');
            }
            const { response, statusCode } = errorResponse(
                'جلسة غير صالحة، الرجاء تسجيل الدخول مرة أخرى',
                401,
                'INVALID_REFRESH_TOKEN'
            );
            return res.status(statusCode).json(response);
        }

        const customer = await Customer.findOne({
            where: {
                CustomerID: decoded.customerId,
                IsBanned: false
            }
        });

        if (!customer) {
            if (req.cookies.customerRefreshToken) {
                res.clearCookie('customerRefreshToken');
            }
            const { response, statusCode } = errorResponse(
                'الحساب غير موجود أو محظور، الرجاء تسجيل الدخول مرة أخرى',
                401,
                'ACCOUNT_NOT_FOUND_OR_BANNED'
            );
            return res.status(statusCode).json(response);
        }

        // Generate new tokens
        const tokens = generateCustomerTokens(customer.CustomerID);

        // Update cookie if it was used
        if (req.cookies.customerRefreshToken) {
            res.cookie('customerRefreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
            });
        }

        // Set CORS headers explicitly
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // Format customer data
        const customerData = {
            id: customer.CustomerID,
            phoneNumber: customer.PhoneNum,
            joinDate: customer.JoinDate,
            status: customer.Status,
            location: {
                latitude: customer.LocationLatitude,
                longitude: customer.LocationLongitude
            }
        };

        // Return the response
        res.status(200).json({
            success: true,
            tokens: {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken
            },
            user: customerData
        });
    } catch (error) {
        console.error('Customer refresh token error:', error);
        if (req.cookies.customerRefreshToken) {
            res.clearCookie('customerRefreshToken');
        }
        const { response, statusCode } = errorResponse(
            'خطأ في الخادم أثناء تحديث الجلسة',
            500,
            'SERVER_ERROR'
        );
        res.status(statusCode).json(response);
    }
};

/**
 * Customer logout
 */
const customerLogout = (_, res) => {
    try {
        res.clearCookie('customerRefreshToken', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
        });

        res.json(successResponse(null, 'تم تسجيل الخروج بنجاح'));
    } catch (error) {
        console.error('Customer logout error:', error);
        const { response, statusCode } = errorResponse(
            'خطأ في الخادم أثناء تسجيل الخروج',
            500,
            'SERVER_ERROR'
        );
        res.status(statusCode).json(response);
    }
};

/**
 * Generate tokens specifically for customer accounts
 */
const generateCustomerTokens = (customerId) => {
    const jwt = require('jsonwebtoken');

    const accessToken = jwt.sign(
        {
            customerId,
            type: 'customer'
        },
        process.env.JWT_CUSTOMER_SECRET || 'customer-secret-key',
        {
            expiresIn: process.env.JWT_CUSTOMER_EXPIRES_IN || '1d'
        }
    );

    const refreshToken = jwt.sign(
        {
            customerId,
            type: 'customer'
        },
        process.env.JWT_CUSTOMER_REFRESH_SECRET || 'customer-refresh-secret-key',
        {
            expiresIn: process.env.JWT_CUSTOMER_REFRESH_EXPIRES_IN || '30d'
        }
    );

    return { accessToken, refreshToken };
};

module.exports = {
    requestPhoneVerification,
    verifyPhoneAndLogin,
    refreshCustomerToken,
    customerLogout
};