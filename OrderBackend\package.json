{"name": "orderbackend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mssql": "^10.0.1", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "sequelize": "^6.35.2", "sharp": "^0.33.5"}, "devDependencies": {"nodemon": "^3.0.2"}}