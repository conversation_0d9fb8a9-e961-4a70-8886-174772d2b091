# SMS Service Troubleshooting Guide

## Issue: SMS Being Sent to iSend Account Owner Instead of Customer

### Root Cause Analysis

The SMS service is correctly configured and sending the customer's phone number to the iSend API. However, the SMS is being delivered to the iSend account owner's number instead of the customer's number. This typically happens due to:

### 1. **Demo/Test API Token**
The current API token in `.env` is a demo token from the iSend documentation:
```
ISEND_API_TOKEN=458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3
```

**Demo tokens often have restrictions:**
- SMS may only be sent to the account owner's registered number
- Limited functionality for testing purposes
- May not support sending to arbitrary phone numbers

### 2. **iSend Account in Test Mode**
Your iSend account might be in test/sandbox mode where:
- All SMS are redirected to the account owner's number
- This is a safety feature to prevent accidental charges during development

### 3. **Account Verification Required**
Some SMS providers require:
- Account verification before allowing SMS to external numbers
- Approval of sender IDs
- Verification of business details

## Solutions

### ✅ **Solution 1: Get Real iSend API Token**

1. **Log into your iSend account** at https://isend.com.ly
2. **Navigate to API settings** or **Developer section**
3. **Generate a new API token** for production use
4. **Replace the demo token** in `.env`:
   ```env
   ISEND_API_TOKEN=your_real_api_token_here
   ```

### ✅ **Solution 2: Verify Account Status**

1. **Check account status** in iSend dashboard
2. **Ensure account is in production mode** (not test/sandbox)
3. **Verify account balance** and payment method
4. **Complete any required verification steps**

### ✅ **Solution 3: Configure Sender ID**

1. **Register your sender ID** with iSend
2. **Update sender ID** in `.env`:
   ```env
   ISEND_SENDER_ID=YourAppName
   ```
3. **Ensure sender ID is approved** by iSend

### ✅ **Solution 4: Test with Different Numbers**

1. **Try sending to different Libya numbers**
2. **Check if issue is specific to certain carriers**
3. **Verify number format** (should be 218XXXXXXXXX)

## Verification Steps

### 1. **Check Current Configuration**
The SMS service now logs detailed information:
```bash
# Start the backend and look for these logs:
📱 SMS Service Configuration:
   API URL: https://isend.com.ly/api/v3/sms/send
   Sender ID: iSend
   API Token: 458|IW09Xh...
   ⚠️  WARNING: Using demo API token!
```

### 2. **Monitor SMS Requests**
When sending SMS, check logs for:
```bash
📤 SMS Request Details:
   Original Phone: +************
   Formatted Phone: ************
   Message: رمز التحقق الخاص بك هو: 1234
   Sender ID: iSend
   API URL: https://isend.com.ly/api/v3/sms/send

📥 iSend API Response:
   Status: success
   Full Response: {...}
```

### 3. **Test SMS Service**
```bash
# Test the SMS service directly
node -e "
const smsService = require('./utils/smsService');
smsService.sendVerificationCode('************').then(console.log);
"
```

## Expected Behavior

### ✅ **Correct Flow:**
1. Customer enters phone number: `**********`
2. Frontend formats to: `+************`
3. Backend receives: `+************`
4. SMS service formats to: `************`
5. iSend API receives: `************`
6. SMS delivered to: `************` ✅

### ❌ **Current Issue:**
1. Customer enters phone number: `**********`
2. Frontend formats to: `+************`
3. Backend receives: `+************`
4. SMS service formats to: `************`
5. iSend API receives: `************`
6. SMS delivered to: **iSend account owner's number** ❌

## Contact iSend Support

If the issue persists after trying the solutions above:

1. **Contact iSend support** with:
   - Your account details
   - API token being used
   - Example request payload
   - Description of the issue

2. **Ask specifically about:**
   - Whether your account is in test mode
   - If the API token has restrictions
   - Account verification requirements
   - Sender ID approval status

## Temporary Workaround

For development/testing, you can:

1. **Use the demo token** for testing the flow
2. **Check logs** to verify correct phone numbers are being sent
3. **Implement the real token** when ready for production

The SMS service is working correctly - the issue is with the iSend account/token configuration, not the code.

## Code Verification

The SMS service correctly:
- ✅ Receives customer phone number from frontend
- ✅ Formats phone number properly (218XXXXXXXXX)
- ✅ Sends correct recipient to iSend API
- ✅ Handles rate limiting (60 seconds)
- ✅ Stores verification codes in Redis
- ✅ Provides detailed logging for debugging

The issue is on the iSend side, not in the application code.
