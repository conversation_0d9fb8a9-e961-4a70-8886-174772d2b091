const Redis = require("ioredis");
const redis = new Redis();

/**
 * SMS Service for sending verification codes using iSend API
 * Handles rate limiting, error handling, and Redis integration
 */
class SMSService {
    constructor() {
        // iSend API configuration
        this.apiUrl = 'https://isend.com.ly/api/v3/sms/send';
        this.apiToken = process.env.ISEND_API_TOKEN || '458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3';
        this.senderId = process.env.ISEND_SENDER_ID || 'iSend';

        // Rate limiting configuration
        this.rateLimitTTL = 60; // 60 seconds timeout for same number
        this.verificationCodeTTL = 300; // 5 minutes for verification code

        // Retry configuration
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second

        // Log configuration on startup
        this.logConfiguration();
    }

    /**
     * Log SMS service configuration for debugging
     */
    logConfiguration() {
        console.log('📱 SMS Service Configuration:');
        console.log(`   API URL: ${this.apiUrl}`);
        console.log(`   Sender ID: ${this.senderId}`);
        console.log(`   API Token: ${this.apiToken.substring(0, 10)}...`);
        console.log(`   Rate Limit TTL: ${this.rateLimitTTL} seconds`);
        console.log(`   Verification Code TTL: ${this.verificationCodeTTL} seconds`);

        // Check if using demo token
        if (this.apiToken === '458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3') {
            console.log('⚠️  WARNING: Using demo API token! SMS may only be sent to account owner.');
            console.log('   Please replace ISEND_API_TOKEN in .env with your actual token.');
        }
    }

    /**
     * Generate a 4-digit verification code
     * @returns {string} 4-digit verification code
     */
    generateVerificationCode() {
        return Math.floor(1000 + Math.random() * 9000).toString();
    }

    /**
     * Check if phone number is rate limited
     * @param {string} phoneNumber - Phone number to check
     * @returns {Promise<boolean>} True if rate limited, false otherwise
     */
    async isRateLimited(phoneNumber) {
        try {
            const rateLimitKey = `sms_rate_limit:${phoneNumber}`;
            const exists = await redis.exists(rateLimitKey);
            return exists === 1;
        } catch (error) {
            console.error('Redis error checking rate limit:', error);
            // If Redis fails, allow the request to proceed
            return false;
        }
    }

    /**
     * Set rate limit for phone number
     * @param {string} phoneNumber - Phone number to rate limit
     * @returns {Promise<void>}
     */
    async setRateLimit(phoneNumber) {
        try {
            const rateLimitKey = `sms_rate_limit:${phoneNumber}`;
            await redis.set(rateLimitKey, '1', 'EX', this.rateLimitTTL);
            console.log(`Rate limit set for ${phoneNumber} for ${this.rateLimitTTL} seconds`);
        } catch (error) {
            console.error('Redis error setting rate limit:', error);
            // Continue execution even if Redis fails
        }
    }

    /**
     * Get remaining rate limit time for phone number
     * @param {string} phoneNumber - Phone number to check
     * @returns {Promise<number>} Remaining seconds, 0 if not rate limited
     */
    async getRateLimitRemaining(phoneNumber) {
        try {
            const rateLimitKey = `sms_rate_limit:${phoneNumber}`;
            const ttl = await redis.ttl(rateLimitKey);
            return ttl > 0 ? ttl : 0;
        } catch (error) {
            console.error('Redis error getting rate limit TTL:', error);
            return 0;
        }
    }

    /**
     * Store verification code in Redis
     * @param {string} phoneNumber - Phone number
     * @param {string} verificationCode - Verification code to store
     * @returns {Promise<void>}
     */
    async storeVerificationCode(phoneNumber, verificationCode) {
        try {
            const verificationData = JSON.stringify({ 
                code: verificationCode,
                timestamp: Date.now()
            });
            await redis.set(
                `verification:${phoneNumber}`, 
                verificationData, 
                'EX', 
                this.verificationCodeTTL
            );
            console.log(`Verification code stored for ${phoneNumber} with TTL of ${this.verificationCodeTTL} seconds`);
        } catch (error) {
            console.error('Redis error storing verification code:', error);
            throw new Error('Failed to store verification code');
        }
    }

    /**
     * Format phone number for Libya (ensure it starts with 218)
     * @param {string} phoneNumber - Raw phone number
     * @returns {string} Formatted phone number
     */
    formatPhoneNumber(phoneNumber) {
        // Remove any non-digit characters
        let cleaned = phoneNumber.replace(/\D/g, '');
        
        // If it starts with 0, replace with 218
        if (cleaned.startsWith('0')) {
            cleaned = '218' + cleaned.substring(1);
        }
        // If it doesn't start with 218, add it
        else if (!cleaned.startsWith('218')) {
            cleaned = '218' + cleaned;
        }
        
        return cleaned;
    }

    /**
     * Validate phone number format
     * @param {string} phoneNumber - Phone number to validate
     * @returns {boolean} True if valid, false otherwise
     */
    validatePhoneNumber(phoneNumber) {
        const formatted = this.formatPhoneNumber(phoneNumber);
        // Libya phone numbers should be 12 digits (218 + 9 digits)
        return /^218\d{9}$/.test(formatted);
    }

    /**
     * Send HTTP request to iSend API with retry logic
     * @param {Object} payload - SMS payload
     * @param {number} retryCount - Current retry attempt
     * @returns {Promise<Object>} API response
     */
    async sendHttpRequest(payload, retryCount = 0) {
        try {
            // Use node-fetch if available, otherwise use built-in fetch (Node.js 18+)
            let fetch;
            try {
                fetch = require('node-fetch');
            } catch (e) {
                // Use built-in fetch for Node.js 18+
                fetch = globalThis.fetch;
            }

            if (!fetch) {
                throw new Error('Fetch is not available. Please install node-fetch or use Node.js 18+');
            }

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            const responseData = await response.json();

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${responseData.message || 'Unknown error'}`);
            }

            return responseData;
        } catch (error) {
            console.error(`SMS API request failed (attempt ${retryCount + 1}):`, error.message);
            
            // Retry logic
            if (retryCount < this.maxRetries) {
                console.log(`Retrying in ${this.retryDelay}ms...`);
                await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                return this.sendHttpRequest(payload, retryCount + 1);
            }
            
            throw error;
        }
    }

    /**
     * Send SMS verification code
     * @param {string} phoneNumber - Phone number to send SMS to
     * @param {string} verificationCode - 4-digit verification code
     * @returns {Promise<Object>} Result object with success status and message
     */
    async sendVerificationSMS(phoneNumber, verificationCode) {
        try {
            // Validate phone number
            if (!this.validatePhoneNumber(phoneNumber)) {
                throw new Error('Invalid phone number format');
            }

            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            
            // Check rate limiting
            if (await this.isRateLimited(formattedNumber)) {
                const remainingTime = await this.getRateLimitRemaining(formattedNumber);
                throw new Error(`Rate limited. Please wait ${remainingTime} seconds before requesting another code.`);
            }

            // Prepare SMS message
            const message = `رمز التحقق الخاص بك هو: ${verificationCode}`;

            const payload = {
                recipient: formattedNumber,
                sender_id: this.senderId,
                type: 'plain',
                message: message
            };

            console.log('📤 SMS Request Details:');
            console.log(`   Original Phone: ${phoneNumber}`);
            console.log(`   Formatted Phone: ${formattedNumber}`);
            console.log(`   Message: ${message}`);
            console.log(`   Sender ID: ${this.senderId}`);
            console.log(`   API URL: ${this.apiUrl}`);
            console.log(`   Payload:`, JSON.stringify(payload, null, 2));

            // Send SMS via iSend API
            const response = await this.sendHttpRequest(payload);

            console.log('📥 iSend API Response:');
            console.log(`   Status: ${response.status}`);
            console.log(`   Full Response:`, JSON.stringify(response, null, 2));

            if (response.status === 'success') {
                // Set rate limit for this number
                await this.setRateLimit(formattedNumber);

                // Store verification code in Redis
                await this.storeVerificationCode(formattedNumber, verificationCode);

                console.log(`✅ SMS sent successfully to ${formattedNumber}`);
                console.log(`   Response Data:`, response.data);

                return {
                    success: true,
                    message: 'SMS sent successfully',
                    data: response.data,
                    sentTo: formattedNumber
                };
            } else {
                console.log(`❌ SMS sending failed:`, response);
                throw new Error(response.message || 'Failed to send SMS');
            }
        } catch (error) {
            console.error('SMS sending error:', error);
            return {
                success: false,
                message: error.message,
                error: error
            };
        }
    }

    /**
     * Send verification code with automatic generation
     * @param {string} phoneNumber - Phone number to send verification code to
     * @returns {Promise<Object>} Result object with success status, message, and verification code (for testing)
     */
    async sendVerificationCode(phoneNumber) {
        try {
            const verificationCode = this.generateVerificationCode();
            const result = await this.sendVerificationSMS(phoneNumber, verificationCode);
            
            if (result.success) {
                // In development mode, include the verification code for testing
                const testingData = process.env.NODE_ENV === 'development' 
                    ? { verificationCode } 
                    : {};
                
                return {
                    success: true,
                    message: 'تم إرسال رمز التحقق بنجاح',
                    phoneNumber: this.formatPhoneNumber(phoneNumber),
                    ...testingData
                };
            } else {
                return result;
            }
        } catch (error) {
            console.error('Error sending verification code:', error);
            return {
                success: false,
                message: 'فشل في إرسال رمز التحقق',
                error: error.message
            };
        }
    }
}

// Create singleton instance
const smsService = new SMSService();

module.exports = smsService;
