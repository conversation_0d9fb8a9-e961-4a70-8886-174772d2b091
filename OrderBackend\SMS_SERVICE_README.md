# SMS Service Documentation

## Overview

The SMS Service is a comprehensive solution for sending 4-digit verification codes via the iSend SMS API. It includes rate limiting, error handling, and seamless integration with the existing customer authentication system.

## Features

- ✅ **4-digit verification code generation**
- ✅ **60-second rate limiting per phone number**
- ✅ **Libya phone number validation and formatting**
- ✅ **Redis integration for code storage and rate limiting**
- ✅ **Comprehensive error handling**
- ✅ **Retry logic for API failures**
- ✅ **Development mode testing support**

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# iSend SMS API Configuration
ISEND_API_TOKEN=your_isend_api_token_here
ISEND_SENDER_ID=your_sender_id_here
```

### Dependencies

The service requires the following npm packages:
- `ioredis` - For Redis integration
- `node-fetch` - For HTTP requests to iSend API

Install with:
```bash
npm install ioredis node-fetch
```

## Usage

### Basic Usage

```javascript
const smsService = require('./utils/smsService');

// Send verification code
const result = await smsService.sendVerificationCode('0*********');

if (result.success) {
    console.log('SMS sent successfully!');
    console.log('Message:', result.message);
    // In development mode, verification code is included
    if (result.verificationCode) {
        console.log('Code:', result.verificationCode);
    }
} else {
    console.log('SMS failed:', result.message);
}
```

### Phone Number Formats

The service automatically handles various Libya phone number formats:

- `0*********` → `218*********`
- `218*********` → `218*********`
- `*********` → `218*********`

### Rate Limiting

- **60-second timeout** between SMS requests for the same number
- Automatic rate limit checking before sending
- Clear error messages when rate limited

### Integration with Customer Auth

The SMS service is integrated with `customerAuthController.js`:

```javascript
// In requestPhoneVerification function
const smsResult = await smsService.sendVerificationCode(phoneNumber.trim());

if (!smsResult.success) {
    // Handle SMS sending failure
    return res.status(500).json(errorResponse(smsResult.message));
}

// SMS sent successfully
res.status(200).json(successResponse({
    phoneNumber: smsResult.phoneNumber,
    message: smsResult.message
}));
```

## API Methods

### `sendVerificationCode(phoneNumber)`

Sends a 4-digit verification code to the specified phone number.

**Parameters:**
- `phoneNumber` (string): Phone number in any Libya format

**Returns:**
```javascript
{
    success: boolean,
    message: string,
    phoneNumber?: string,      // Formatted phone number
    verificationCode?: string  // Only in development mode
}
```

### `validatePhoneNumber(phoneNumber)`

Validates if a phone number is in correct Libya format.

**Parameters:**
- `phoneNumber` (string): Phone number to validate

**Returns:** `boolean`

### `formatPhoneNumber(phoneNumber)`

Formats a phone number to Libya international format (218XXXXXXXXX).

**Parameters:**
- `phoneNumber` (string): Phone number to format

**Returns:** `string` - Formatted phone number

### `isRateLimited(phoneNumber)`

Checks if a phone number is currently rate limited.

**Parameters:**
- `phoneNumber` (string): Phone number to check

**Returns:** `Promise<boolean>`

### `getRateLimitRemaining(phoneNumber)`

Gets remaining seconds until rate limit expires.

**Parameters:**
- `phoneNumber` (string): Phone number to check

**Returns:** `Promise<number>` - Remaining seconds

## Error Handling

The service handles various error scenarios:

1. **Invalid phone number format**
2. **Rate limiting (60-second timeout)**
3. **iSend API failures**
4. **Redis connection issues**
5. **Network connectivity problems**

All errors are logged and return descriptive error messages.

## Testing

### Manual Testing

Run the test script:
```bash
node test-sms.js
```

This will test:
- Phone number validation and formatting
- SMS sending functionality
- Rate limiting behavior
- Error handling

### Development Mode

In development mode (`NODE_ENV=development`), the verification code is included in the response for testing purposes.

**⚠️ Warning:** Never include verification codes in production responses!

## Redis Keys

The service uses the following Redis keys:

- `verification:{phoneNumber}` - Stores verification codes (5-minute TTL)
- `sms_rate_limit:{phoneNumber}` - Rate limiting (60-second TTL)

## Security Considerations

1. **API Token Security**: Store iSend API token securely in environment variables
2. **Rate Limiting**: 60-second timeout prevents SMS spam
3. **Code Expiration**: Verification codes expire after 5 minutes
4. **Production Safety**: Verification codes are not returned in production responses

## Troubleshooting

### Common Issues

1. **"Fetch is not available"**
   - Install node-fetch: `npm install node-fetch`
   - Or use Node.js 18+ with built-in fetch

2. **"Rate limited" errors**
   - Wait 60 seconds between requests for the same number
   - Check Redis connection

3. **"Invalid phone number format"**
   - Ensure phone number is Libya format
   - Use 9-digit local numbers (e.g., *********)

4. **iSend API errors**
   - Verify API token in .env file
   - Check iSend account balance
   - Verify sender ID configuration

### Logs

The service provides detailed logging:
- SMS sending attempts
- Rate limiting actions
- API responses
- Error details

Check console output for debugging information.

## Support

For issues related to:
- **iSend API**: Contact iSend support
- **SMS Service**: Check logs and error messages
- **Integration**: Review customerAuthController.js implementation
