give me this Doc , but very orginized for to give to AI Prompt to make him understand it : 

"API Endpoint

Markup

https://isend.com.ly/api/v3/sms/send

Parameters

ParameterمطلوبالوصفAuthorizationنعمWhen calling our API, send your api token with the authentication type set as Bearer (Example: Authorization: Bearer {api_token})AcceptنعمSet to application/json

Send outbound SMS

iSend SMS's Programmable SMS API enables you to programmatically send SMS messages from your web application. First, you need to create a new message object. iSend SMS returns the created message object with each request.

Send your first SMS message with this example request.

API Endpoint

Markup

https://isend.com.ly/api/v3/sms/send

Parameters

ParameterمطلوبالنوعالوصفrecipientنعمstringNumber to send message. Use comma (,) to send multiple numbers. Ex. 218929000835,218929000834sender_idنعمstringThe sender of the message. This can be a telephone number (including country code) or an alphanumeric string. In case of an alphanumeric string, the maximum length is 11 characters.typeنعمstringThe type of the message. For text message you have to insert plain as sms type.messageنعمstringThe body of the SMS message.schedule_timeلاdatetimeThe scheduled date and time of the message in RFC3339 format (Y-m-d H:i)dlt_template_idلاstringThe ID of your registered DLT (Distributed Ledger Technology) content template.

Example request for Single Number

PHP



curl -X POST https://isend.com.ly/api/v3/sms/send \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \-d '{"recipient":"218929000835","sender_id":"iSend","type":"plain","message":"This is a test message"}'

Example request for Multiple Numbers

PHP



curl -X POST https://isend.com.ly/api/v3/sms/send \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \-d '{"recipient":"218929000835,218929000834","sender_id":"iSend","type":"plain","message":"This is a test message","schedule_time=2021-12-20 07:00"}'

Returns

Returns a contact object if the request was successful.

JSON

{    "status": "success",    "data": "sms reports with all details",}

If the request failed, an error object will be returned.

JSON

{    "status": "error",    "message" : "A human-readable description of the error."}

Send Campaign Using Contact list

iSend SMS's Programmable SMS API enables you to programmatically send Campaigns from your web application. First, you need to create a new message object. iSend SMS returns the created message object with each request.

Send your first Campaign Using Contact List with this example request.

API Endpoint

Markup

https://isend.com.ly/api/v3/sms/campaign

Parameters

Parameterمطلوبالنوعالوصفcontact_list_idنعمstringContact list to send message. Use comma (,) to send multiple contact lists. Ex. 6415907d0d7a6,6415907d0d37asender_idنعمstringThe sender of the message. This can be a telephone number (including country code) or an alphanumeric string. In case of an alphanumeric string, the maximum length is 11 characters.typeنعمstringThe type of the message. For text message you have to insert plain as sms type.messageنعمstringThe body of the SMS message.schedule_timeلاdatetimeThe scheduled date and time of the message in RFC3339 format (Y-m-d H:i)dlt_template_idلاstringThe ID of your registered DLT (Distributed Ledger Technology) content template.

Example request for Single Contact List

PHP



curl -X POST https://isend.com.ly/api/v3/sms/campaign \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \-d '{"recipient":"6415907d0d37a","sender_id":"iSend","type":"plain","message":"This is a test message"}'

Example request for Multiple Contact Lists

PHP



curl -X POST https://isend.com.ly/api/v3/sms/campaign \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \-d '{"recipient":"6415907d0d37a,6415907d0d7a6","sender_id":"iSend","type":"plain","message":"This is a test message","schedule_time=2021-12-20 07:00"}'

Returns

Returns a contact object if the request was successful.

JSON

{    "status": "success",    "data": "campaign reports with all details",}

If the request failed, an error object will be returned.

JSON

{    "status": "error",    "message" : "A human-readable description of the error."}

View an SMS

You can use iSend SMS's SMS API to retrieve information of an existing inbound or outbound SMS message.

You only need to supply the unique message id that was returned upon creation or receiving.

API Endpoint

Markup

https://isend.com.ly/api/v3/sms/{uid}

Parameters

ParameterمطلوبالنوعالوصفuidنعمstringA unique random uid which is created on the iSend SMS platform and is returned upon creation of the object.

Example request

PHP



curl -X GET https://isend.com.ly/api/v3/sms/606812e63f78b \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \

Returns

Returns a contact object if the request was successful.

JSON

{    "status": "success",    "data": "sms data with all details",}

If the request failed, an error object will be returned.

JSON

{    "status": "error",    "message" : "A human-readable description of the error."}

View all messages

API Endpoint

Markup

https://isend.com.ly/api/v3/sms/

Example request

PHP



curl -X GET https://isend.com.ly/api/v3/sms \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \

Returns

Returns a contact object if the request was successful.

JSON

{    "status": "success",    "data": "sms reports with pagination",}

If the request failed, an error object will be returned.

JSON

{    "status": "error",    "message" : "A human-readable description of the error."}

View Campaign

You can use iSend SMS's Campaign API to retrieve information of an existing Campaigns.

You only need to supply the unique campaign id that was returned upon creation or receiving.

API Endpoint

Markup

https://isend.com.ly/api/v3/campaign/{uid}

Parameters

ParameterمطلوبالنوعالوصفuidنعمstringA unique random uid which is created on the iSend SMS platform and is returned upon creation of the object.

Example request

PHP



curl -X GET https://isend.com.ly/api/v3/campaign/606812e63f78b/view \-H 'Authorization: Bearer 458|IW09Xhkr4HEeVkWtiWiaymGLjtn1HVLULYLwCvfB0b19c8c3' \-H 'Content-Type: application/json' \-H 'Accept: application/json' \

Returns

Returns a contact object if the request was successful.

JSON

{    "status": "success",    "data": "campaign data with all details",}

If the request failed, an error object will be returned.

JSON

{    "status": "error",    "message" : "A human-readable description of the error."}"